{"hooks": {"PreToolUse": [{"matcher": "Task", "hooks": [{"type": "command", "command": "Task"}]}, {"matcher": "codebase-retrieval", "hooks": [{"type": "command", "command": "echo '🔍 [MAIN ORCHESTRATOR] Analyzing codebase for agent delegation opportunities...'"}]}, {"matcher": "str-replace-editor", "hooks": [{"type": "command", "command": "echo '✏️ [MAIN ORCHESTRATOR] Code modification detected - evaluating Code Reviewer delegation...'"}]}], "PostToolUse": [{"matcher": "save-file", "hooks": [{"type": "command", "command": "echo '💾 [MAIN ORCHESTRATOR] New file created - evaluating delegation needs for integration...'"}]}, {"matcher": "launch-process", "hooks": [{"type": "command", "command": "echo '🚀 [MAIN ORCHESTRATOR] Process launched - Backend Architect may need to review system integration...'"}]}, {"matcher": "web-search", "hooks": [{"type": "command", "command": "echo '🌐 [MAIN ORCHESTRATOR] Web search completed - Web Researcher agent should analyze findings...'"}]}], "AgentDelegation": [{"matcher": "AI|ML|algorithm|model|neural|prediction|tensorflow|pytorch|scikit", "hooks": [{"type": "command", "command": "echo '🤖 [DELEGATION] AI/ML task detected → Delegating to Senior AI Engineer agent'"}]}, {"matcher": "security|audit|review|vulnerability|quality|test|validation", "hooks": [{"type": "command", "command": "echo '🔍 [DELEGATION] Security/Quality task detected → Delegating to Code Reviewer agent'"}]}, {"matcher": "architecture|database|scaling|infrastructure|microservices|docker|kubernetes", "hooks": [{"type": "command", "command": "echo '🏗️ [DELEGATION] Architecture task detected → Delegating to Senior Backend Architect agent'"}]}, {"matcher": "solana|blockchain|wallet|memecoin|DEX|token|web3|anchor|jupiter", "hooks": [{"type": "command", "command": "echo '⚡ [DELEGATION] Solana/Blockchain task detected → Delegating to Solana Memecoins Wallets agent'"}]}, {"matcher": "risk|portfolio|compliance|VaR|position|drawdown|volatility", "hooks": [{"type": "command", "command": "echo '🛡️ [DELEGATION] Risk Management task detected → Delegating to Risk Management agent'"}]}, {"matcher": "API|gRPC|GraphQL|service|endpoint|communication|REST|websocket", "hooks": [{"type": "command", "command": "echo '🔗 [DELEGATION] API/Service task detected → Delegating to gRPC/GraphQL Management agent'"}]}, {"matcher": "research|market|trend|competitive|analysis|sentiment|social", "hooks": [{"type": "command", "command": "echo '🔬 [DELEGATION] Research task detected → Delegating to Web Researcher agent'"}]}, {"matcher": "telegram|social|community|notification|bot|discord|twitter", "hooks": [{"type": "command", "command": "echo '📱 [DELEGATION] Social/Telegram task detected → Delegating to Bloom/Telegram agent'"}]}], "MultiAgentWorkflow": [{"matcher": "feature.development|new.feature|implement.feature", "hooks": [{"type": "command", "command": "echo '🚀 [MULTI-AGENT WORKFLOW] New Feature Development initiated:'"}, {"type": "command", "command": "echo '   1. Web Researcher → Market analysis and user needs'"}, {"type": "command", "command": "echo '   2. Senior AI Engineer → Algorithm development'"}, {"type": "command", "command": "echo '   3. Backend Architect → System design'"}, {"type": "command", "command": "echo '   4. Solana Agent → Blockchain integration'"}, {"type": "command", "command": "echo '   5. Risk Management → Safety protocols'"}, {"type": "command", "command": "echo '   6. gRPC/GraphQL → API integration'"}, {"type": "command", "command": "echo '   7. Code Reviewer → Security audit'"}, {"type": "command", "command": "echo '   8. Bloom/Telegram → User interface'"}]}, {"matcher": "trading.strategy|strategy.implementation|new.strategy", "hooks": [{"type": "command", "command": "echo '📈 [MULTI-AGENT WORKFLOW] Trading Strategy Implementation initiated:'"}, {"type": "command", "command": "echo '   1. Web Researcher → Market trend analysis'"}, {"type": "command", "command": "echo '   2. Senior AI Engineer → Algorithm development'"}, {"type": "command", "command": "echo '   3. Risk Management → Risk parameters'"}, {"type": "command", "command": "echo '   4. Solana Agent → Blockchain execution'"}, {"type": "command", "command": "echo '   5. Backend Architect → Performance optimization'"}, {"type": "command", "command": "echo '   6. Code Reviewer → Security validation'"}, {"type": "command", "command": "echo '   7. gRPC/GraphQL → Real-time data feeds'"}, {"type": "command", "command": "echo '   8. Bloom/Telegram → User alerts'"}]}], "QualityAssurance": [{"matcher": "commit|merge|deploy|release", "hooks": [{"type": "command", "command": "echo '✅ [QUALITY GATE] Code Reviewer agent should validate before proceeding...'"}]}, {"matcher": "production|live|mainnet", "hooks": [{"type": "command", "command": "echo '🚨 [PRODUCTION ALERT] Risk Management agent must approve production changes...'"}]}], "ProjectMonitoring": [{"matcher": "error|failure|exception|crash", "hooks": [{"type": "command", "command": "echo '🚨 [INCIDENT] Main Orchestrator coordinating emergency response...'"}]}, {"matcher": "success|complete|finished|deployed", "hooks": [{"type": "command", "command": "echo '🎉 [SUCCESS] Main Orchestrator logging successful completion...'"}]}]}}