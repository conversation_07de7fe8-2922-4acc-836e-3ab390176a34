#!/bin/bash

# Quality Gate Hook Script
# This script enforces quality gates and triggers appropriate agent reviews

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to log quality gate events
log_quality_gate() {
    local gate_type="$1"
    local status="$2"
    local details="$3"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] QUALITY_GATE: $gate_type | STATUS: $status | DETAILS: $details" >> .claude/logs/quality-gates.log
}

# Function to check if Code Reviewer should be triggered
check_code_review_needed() {
    local operation="$1"
    local files_changed="$2"
    
    # Critical operations that always need review
    if echo "$operation" | grep -qE "(commit|merge|deploy|release|production)"; then
        echo -e "${RED}🚨 [QUALITY GATE] Critical operation detected: $operation${NC}"
        echo -e "${YELLOW}   → Code Reviewer agent MUST validate before proceeding${NC}"
        log_quality_gate "CODE_REVIEW" "REQUIRED" "$operation"
        return 0
    fi
    
    # Check file types that need review
    if echo "$files_changed" | grep -qE "\.(js|ts|py|rs|go|java|sol)$"; then
        echo -e "${BLUE}🔍 [QUALITY GATE] Source code changes detected${NC}"
        echo -e "${YELLOW}   → Code Reviewer agent should validate changes${NC}"
        log_quality_gate "CODE_REVIEW" "RECOMMENDED" "Source code changes"
        return 0
    fi
    
    # Security-sensitive files
    if echo "$files_changed" | grep -qE "(wallet|key|secret|config|env|security)"; then
        echo -e "${RED}🔒 [QUALITY GATE] Security-sensitive files detected${NC}"
        echo -e "${YELLOW}   → Code Reviewer agent MUST perform security audit${NC}"
        log_quality_gate "SECURITY_REVIEW" "REQUIRED" "Security-sensitive files"
        return 0
    fi
    
    return 1
}

# Function to check if Risk Management should be triggered
check_risk_management_needed() {
    local operation="$1"
    local context="$2"
    
    # Production deployments
    if echo "$operation" | grep -qE "(production|live|mainnet|deploy)"; then
        echo -e "${RED}🛡️ [QUALITY GATE] Production deployment detected${NC}"
        echo -e "${YELLOW}   → Risk Management agent MUST approve production changes${NC}"
        log_quality_gate "RISK_MANAGEMENT" "REQUIRED" "Production deployment"
        return 0
    fi
    
    # Trading strategy changes
    if echo "$context" | grep -qE "(trading|strategy|algorithm|portfolio|risk)"; then
        echo -e "${PURPLE}📊 [QUALITY GATE] Trading-related changes detected${NC}"
        echo -e "${YELLOW}   → Risk Management agent should assess impact${NC}"
        log_quality_gate "RISK_ASSESSMENT" "RECOMMENDED" "Trading changes"
        return 0
    fi
    
    # Financial operations
    if echo "$context" | grep -qE "(wallet|balance|transaction|fund|money)"; then
        echo -e "${RED}💰 [QUALITY GATE] Financial operations detected${NC}"
        echo -e "${YELLOW}   → Risk Management agent MUST validate financial safety${NC}"
        log_quality_gate "FINANCIAL_REVIEW" "REQUIRED" "Financial operations"
        return 0
    fi
    
    return 1
}

# Function to check if Backend Architect should be triggered
check_architecture_review_needed() {
    local operation="$1"
    local context="$2"
    
    # Infrastructure changes
    if echo "$context" | grep -qE "(infrastructure|database|scaling|architecture|microservice)"; then
        echo -e "${CYAN}🏗️ [QUALITY GATE] Architecture changes detected${NC}"
        echo -e "${YELLOW}   → Backend Architect should review system impact${NC}"
        log_quality_gate "ARCHITECTURE_REVIEW" "RECOMMENDED" "Architecture changes"
        return 0
    fi
    
    # Performance-critical changes
    if echo "$context" | grep -qE "(performance|optimization|latency|throughput)"; then
        echo -e "${BLUE}⚡ [QUALITY GATE] Performance changes detected${NC}"
        echo -e "${YELLOW}   → Backend Architect should assess performance impact${NC}"
        log_quality_gate "PERFORMANCE_REVIEW" "RECOMMENDED" "Performance changes"
        return 0
    fi
    
    return 1
}

# Function to run comprehensive quality checks
run_quality_checks() {
    local operation="$1"
    local context="$2"
    local files_changed="$3"
    
    echo -e "${GREEN}✅ [QUALITY GATE] Running comprehensive quality checks...${NC}"
    echo -e "${CYAN}   Operation: $operation${NC}"
    echo -e "${CYAN}   Context: $context${NC}"
    echo -e "${CYAN}   Files: $files_changed${NC}"
    echo ""
    
    local checks_triggered=0
    
    # Check if code review is needed
    if check_code_review_needed "$operation" "$files_changed"; then
        ((checks_triggered++))
    fi
    
    # Check if risk management is needed
    if check_risk_management_needed "$operation" "$context"; then
        ((checks_triggered++))
    fi
    
    # Check if architecture review is needed
    if check_architecture_review_needed "$operation" "$context"; then
        ((checks_triggered++))
    fi
    
    # Summary
    if [ $checks_triggered -eq 0 ]; then
        echo -e "${GREEN}✅ [QUALITY GATE] No additional reviews required${NC}"
        log_quality_gate "QUALITY_CHECK" "PASSED" "No reviews needed"
    else
        echo -e "${YELLOW}📋 [QUALITY GATE] $checks_triggered quality check(s) triggered${NC}"
        log_quality_gate "QUALITY_CHECK" "REVIEWS_REQUIRED" "$checks_triggered checks triggered"
    fi
}

# Function to show quality gate status
show_quality_gate_status() {
    echo -e "${BLUE}📊 [QUALITY GATE STATUS]${NC}"
    echo -e "${CYAN}═══════════════════════════${NC}"
    
    # Check if logs exist
    if [ -f ".claude/logs/quality-gates.log" ]; then
        echo -e "${GREEN}Recent Quality Gate Events:${NC}"
        tail -5 .claude/logs/quality-gates.log | while read line; do
            echo -e "   ${YELLOW}$line${NC}"
        done
    else
        echo -e "${YELLOW}No quality gate events logged yet${NC}"
    fi
    
    echo ""
    echo -e "${PURPLE}Quality Gate Rules:${NC}"
    echo -e "   ${RED}🚨 REQUIRED:${NC} Production deployments, security files, financial operations"
    echo -e "   ${YELLOW}📋 RECOMMENDED:${NC} Source code changes, trading strategies, architecture changes"
    echo -e "   ${GREEN}✅ OPTIONAL:${NC} Documentation, configuration, non-critical changes"
}

# Function to simulate quality gate for testing
simulate_quality_gate() {
    local scenario="$1"
    
    echo -e "${PURPLE}🧪 [QUALITY GATE SIMULATION] Testing scenario: $scenario${NC}"
    
    case "$scenario" in
        "production_deploy")
            run_quality_checks "deploy" "production mainnet release" "trading-engine.js wallet-manager.py"
            ;;
        "security_change")
            run_quality_checks "commit" "security wallet authentication" "wallet-security.js auth-config.json"
            ;;
        "trading_strategy")
            run_quality_checks "implement" "trading strategy algorithm" "momentum-strategy.py risk-calculator.js"
            ;;
        "architecture_update")
            run_quality_checks "refactor" "microservices architecture database" "api-gateway.js database-schema.sql"
            ;;
        *)
            echo -e "${RED}Unknown simulation scenario: $scenario${NC}"
            echo -e "${YELLOW}Available scenarios: production_deploy, security_change, trading_strategy, architecture_update${NC}"
            ;;
    esac
}

# Main execution
if [ $# -eq 0 ]; then
    echo -e "${RED}Usage: $0 <operation> [context] [files_changed]${NC}"
    echo -e "${YELLOW}   or: $0 status${NC}"
    echo -e "${YELLOW}   or: $0 simulate <scenario>${NC}"
    echo ""
    echo -e "${CYAN}Examples:${NC}"
    echo -e "   ${BLUE}$0 commit 'trading strategy update' 'strategy.py risk.js'${NC}"
    echo -e "   ${BLUE}$0 deploy 'production release' 'all-files'${NC}"
    echo -e "   ${BLUE}$0 status${NC}"
    echo -e "   ${BLUE}$0 simulate production_deploy${NC}"
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p .claude/logs

# Handle different commands
case "$1" in
    "status")
        show_quality_gate_status
        ;;
    "simulate")
        if [ $# -lt 2 ]; then
            echo -e "${RED}Error: Simulation scenario required${NC}"
            exit 1
        fi
        simulate_quality_gate "$2"
        ;;
    *)
        operation="$1"
        context="${2:-unknown}"
        files_changed="${3:-unknown}"
        run_quality_checks "$operation" "$context" "$files_changed"
        ;;
esac

echo -e "\n${BLUE}📋 Quality gate check complete. Check .claude/logs/quality-gates.log for details.${NC}"
