#!/bin/bash

# Test Script for Agent Delegation System
# This script demonstrates the delegation system functionality

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 [DELEGATION SYSTEM TEST] Starting comprehensive test suite...${NC}"
echo -e "${CYAN}═══════════════════════════════════════════════════════════${NC}"

# Create logs directory if it doesn't exist
mkdir -p .claude/logs

# Test 1: AI/ML Task Delegation
echo -e "\n${GREEN}Test 1: AI/ML Task Delegation${NC}"
echo -e "${YELLOW}Input: 'Implement machine learning model for price prediction'${NC}"
.claude/hooks/agent-delegation.sh "Implement machine learning model for price prediction"

# Test 2: Security Task Delegation
echo -e "\n${GREEN}Test 2: Security Task Delegation${NC}"
echo -e "${YELLOW}Input: 'Review smart contract for security vulnerabilities'${NC}"
.claude/hooks/agent-delegation.sh "Review smart contract for security vulnerabilities"

# Test 3: Solana/Blockchain Task Delegation
echo -e "\n${GREEN}Test 3: Solana/Blockchain Task Delegation${NC}"
echo -e "${YELLOW}Input: 'Create wallet integration for Jupiter DEX'${NC}"
.claude/hooks/agent-delegation.sh "Create wallet integration for Jupiter DEX"

# Test 4: Risk Management Task Delegation
echo -e "\n${GREEN}Test 4: Risk Management Task Delegation${NC}"
echo -e "${YELLOW}Input: 'Calculate portfolio VaR and position sizing'${NC}"
.claude/hooks/agent-delegation.sh "Calculate portfolio VaR and position sizing"

# Test 5: Multi-Agent Workflow - Feature Development
echo -e "\n${GREEN}Test 5: Multi-Agent Workflow - Feature Development${NC}"
echo -e "${YELLOW}Input: 'feature.development'${NC}"
.claude/hooks/workflow-coordinator.sh feature.development

# Test 6: Multi-Agent Workflow - Trading Strategy
echo -e "\n${GREEN}Test 6: Multi-Agent Workflow - Trading Strategy${NC}"
echo -e "${YELLOW}Input: 'trading.strategy'${NC}"
.claude/hooks/workflow-coordinator.sh trading.strategy

# Test 7: Quality Gate - Production Deployment
echo -e "\n${GREEN}Test 7: Quality Gate - Production Deployment${NC}"
echo -e "${YELLOW}Input: Production deployment with security files${NC}"
.claude/hooks/quality-gate.sh deploy "production mainnet release" "trading-engine.js wallet-manager.py"

# Test 8: Quality Gate - Security Changes
echo -e "\n${GREEN}Test 8: Quality Gate - Security Changes${NC}"
echo -e "${YELLOW}Input: Security-sensitive file changes${NC}"
.claude/hooks/quality-gate.sh commit "security wallet authentication" "wallet-security.js auth-config.json"

# Test 9: Quality Gate Status Check
echo -e "\n${GREEN}Test 9: Quality Gate Status Check${NC}"
.claude/hooks/quality-gate.sh status

# Test 10: Complex Multi-Agent Scenario
echo -e "\n${GREEN}Test 10: Complex Multi-Agent Scenario${NC}"
echo -e "${YELLOW}Input: 'Implement new memecoin trading strategy with AI sentiment analysis and risk management'${NC}"
.claude/hooks/agent-delegation.sh "Implement new memecoin trading strategy with AI sentiment analysis and risk management"

# Display test results summary
echo -e "\n${BLUE}📊 [TEST RESULTS SUMMARY]${NC}"
echo -e "${CYAN}═══════════════════════════${NC}"

if [ -f ".claude/logs/delegation.log" ]; then
    echo -e "${GREEN}✅ Delegation Log Created:${NC}"
    echo -e "   ${YELLOW}$(wc -l < .claude/logs/delegation.log) delegation events logged${NC}"
    echo -e "   ${CYAN}Latest delegations:${NC}"
    tail -3 .claude/logs/delegation.log | while read line; do
        echo -e "   ${YELLOW}$line${NC}"
    done
else
    echo -e "${RED}❌ Delegation log not found${NC}"
fi

if [ -f ".claude/logs/workflow.log" ]; then
    echo -e "\n${GREEN}✅ Workflow Log Created:${NC}"
    echo -e "   ${YELLOW}$(wc -l < .claude/logs/workflow.log) workflow events logged${NC}"
    echo -e "   ${CYAN}Latest workflows:${NC}"
    tail -3 .claude/logs/workflow.log | while read line; do
        echo -e "   ${YELLOW}$line${NC}"
    done
else
    echo -e "${RED}❌ Workflow log not found${NC}"
fi

if [ -f ".claude/logs/quality-gates.log" ]; then
    echo -e "\n${GREEN}✅ Quality Gates Log Created:${NC}"
    echo -e "   ${YELLOW}$(wc -l < .claude/logs/quality-gates.log) quality gate events logged${NC}"
    echo -e "   ${CYAN}Latest quality gates:${NC}"
    tail -3 .claude/logs/quality-gates.log | while read line; do
        echo -e "   ${YELLOW}$line${NC}"
    done
else
    echo -e "${RED}❌ Quality gates log not found${NC}"
fi

# Test agent files exist
echo -e "\n${BLUE}🔍 [AGENT FILES VERIFICATION]${NC}"
echo -e "${CYAN}═══════════════════════════════${NC}"

agents=(
    "main-orchestrator"
    "senior-ai-engineer"
    "code-reviewer"
    "senior-backend-architect"
    "solana-memecoins-wallets-agent"
    "risk-management-agent"
    "grpc-graphql-management-agent"
    "web-researcher-agent"
    "bloom-telegram-agent"
)

for agent in "${agents[@]}"; do
    if [ -f ".claude/agents/${agent}.md" ]; then
        echo -e "   ${GREEN}✅ ${agent}.md${NC}"
    else
        echo -e "   ${RED}❌ ${agent}.md${NC}"
    fi
done

# Test hooks configuration
echo -e "\n${BLUE}⚙️ [HOOKS CONFIGURATION VERIFICATION]${NC}"
echo -e "${CYAN}═══════════════════════════════════════${NC}"

if [ -f ".claude/settings.json" ]; then
    echo -e "   ${GREEN}✅ settings.json exists${NC}"
    
    # Check for key hook sections
    if grep -q "PreToolUse" .claude/settings.json; then
        echo -e "   ${GREEN}✅ PreToolUse hooks configured${NC}"
    else
        echo -e "   ${RED}❌ PreToolUse hooks missing${NC}"
    fi
    
    if grep -q "PostToolUse" .claude/settings.json; then
        echo -e "   ${GREEN}✅ PostToolUse hooks configured${NC}"
    else
        echo -e "   ${RED}❌ PostToolUse hooks missing${NC}"
    fi
    
    if grep -q "AgentDelegation" .claude/settings.json; then
        echo -e "   ${GREEN}✅ AgentDelegation hooks configured${NC}"
    else
        echo -e "   ${RED}❌ AgentDelegation hooks missing${NC}"
    fi
else
    echo -e "   ${RED}❌ settings.json not found${NC}"
fi

# Test hook scripts
hook_scripts=(
    "agent-delegation.sh"
    "workflow-coordinator.sh"
    "quality-gate.sh"
)

for script in "${hook_scripts[@]}"; do
    if [ -f ".claude/hooks/${script}" ] && [ -x ".claude/hooks/${script}" ]; then
        echo -e "   ${GREEN}✅ ${script} (executable)${NC}"
    elif [ -f ".claude/hooks/${script}" ]; then
        echo -e "   ${YELLOW}⚠️ ${script} (not executable)${NC}"
    else
        echo -e "   ${RED}❌ ${script} missing${NC}"
    fi
done

echo -e "\n${GREEN}🎉 [DELEGATION SYSTEM TEST COMPLETE]${NC}"
echo -e "${CYAN}═══════════════════════════════════════${NC}"
echo -e "${BLUE}The delegation system is ready for use!${NC}"
echo -e "${YELLOW}Check the logs in .claude/logs/ for detailed delegation history.${NC}"
