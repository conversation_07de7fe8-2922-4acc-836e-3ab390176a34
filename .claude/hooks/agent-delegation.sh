#!/bin/bash

# Agent Delegation Hook Script
# This script analyzes user input and suggests appropriate agent delegation

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to log delegation decisions
log_delegation() {
    local agent="$1"
    local reason="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] DELEGATION: $agent - $reason" >> .claude/logs/delegation.log
}

# Function to analyze input and suggest agent
analyze_and_delegate() {
    local input="$1"
    local suggested_agent=""
    local confidence=""
    local reason=""

    # Convert to lowercase for analysis
    local lower_input=$(echo "$input" | tr '[:upper:]' '[:lower:]')

    # AI/ML Keywords Analysis
    if echo "$lower_input" | grep -qE "(ai|ml|machine.learning|neural|algorithm|model|prediction|tensorflow|pytorch|scikit|reinforcement|deep.learning)"; then
        suggested_agent="senior-ai-engineer"
        confidence="HIGH"
        reason="AI/ML keywords detected"
    
    # Security/Quality Keywords Analysis
    elif echo "$lower_input" | grep -qE "(security|audit|review|vulnerability|quality|test|validation|penetration|exploit)"; then
        suggested_agent="code-reviewer"
        confidence="HIGH"
        reason="Security/Quality keywords detected"
    
    # Architecture Keywords Analysis
    elif echo "$lower_input" | grep -qE "(architecture|database|scaling|infrastructure|microservices|docker|kubernetes|redis|postgresql)"; then
        suggested_agent="senior-backend-architect"
        confidence="HIGH"
        reason="Architecture keywords detected"
    
    # Solana/Blockchain Keywords Analysis
    elif echo "$lower_input" | grep -qE "(solana|blockchain|wallet|memecoin|dex|token|web3|anchor|jupiter|raydium|orca)"; then
        suggested_agent="solana-memecoins-wallets-agent"
        confidence="HIGH"
        reason="Solana/Blockchain keywords detected"
    
    # Risk Management Keywords Analysis
    elif echo "$lower_input" | grep -qE "(risk|portfolio|compliance|var|position|drawdown|volatility|sharpe|kelly|diversification)"; then
        suggested_agent="risk-management-agent"
        confidence="HIGH"
        reason="Risk Management keywords detected"
    
    # API/Service Keywords Analysis
    elif echo "$lower_input" | grep -qE "(api|grpc|graphql|service|endpoint|communication|rest|websocket|protobuf|schema)"; then
        suggested_agent="grpc-graphql-management-agent"
        confidence="HIGH"
        reason="API/Service keywords detected"
    
    # Research Keywords Analysis
    elif echo "$lower_input" | grep -qE "(research|market|trend|competitive|analysis|sentiment|social|intelligence|survey)"; then
        suggested_agent="web-researcher-agent"
        confidence="HIGH"
        reason="Research keywords detected"
    
    # Social/Telegram Keywords Analysis
    elif echo "$lower_input" | grep -qE "(telegram|social|community|notification|bot|discord|twitter|engagement|messaging)"; then
        suggested_agent="bloom-telegram-agent"
        confidence="HIGH"
        reason="Social/Telegram keywords detected"
    
    # Multi-agent workflow detection
    elif echo "$lower_input" | grep -qE "(feature.development|new.feature|implement.feature)"; then
        suggested_agent="MULTI_AGENT_WORKFLOW"
        confidence="HIGH"
        reason="New feature development workflow detected"
    
    elif echo "$lower_input" | grep -qE "(trading.strategy|strategy.implementation|new.strategy)"; then
        suggested_agent="MULTI_AGENT_WORKFLOW"
        confidence="HIGH"
        reason="Trading strategy implementation workflow detected"
    
    else
        suggested_agent="main-orchestrator"
        confidence="MEDIUM"
        reason="No specific agent keywords detected - using main orchestrator"
    fi

    # Output delegation suggestion
    echo -e "${BLUE}🎯 [DELEGATION ANALYSIS]${NC}"
    echo -e "   ${CYAN}Suggested Agent:${NC} $suggested_agent"
    echo -e "   ${YELLOW}Confidence:${NC} $confidence"
    echo -e "   ${GREEN}Reason:${NC} $reason"
    
    # Log the delegation decision
    log_delegation "$suggested_agent" "$reason"
    
    # Return the suggested agent for further processing
    echo "$suggested_agent"
}

# Function to display agent capabilities
show_agent_capabilities() {
    local agent="$1"
    
    case "$agent" in
        "senior-ai-engineer")
            echo -e "${PURPLE}🤖 Senior AI Engineer Capabilities:${NC}"
            echo "   • Machine learning model development"
            echo "   • Trading algorithm optimization"
            echo "   • Neural network implementation"
            echo "   • Predictive analytics"
            ;;
        "code-reviewer")
            echo -e "${RED}🔍 Code Reviewer Capabilities:${NC}"
            echo "   • Security vulnerability assessment"
            echo "   • Code quality audits"
            echo "   • Best practices enforcement"
            echo "   • Trading-specific validations"
            ;;
        "senior-backend-architect")
            echo -e "${CYAN}🏗️ Senior Backend Architect Capabilities:${NC}"
            echo "   • System architecture design"
            echo "   • Database optimization"
            echo "   • Microservices implementation"
            echo "   • Infrastructure scaling"
            ;;
        "solana-memecoins-wallets-agent")
            echo -e "${YELLOW}⚡ Solana Agent Capabilities:${NC}"
            echo "   • Blockchain integration"
            echo "   • Wallet management"
            echo "   • DEX interactions"
            echo "   • Memecoin analysis"
            ;;
        "risk-management-agent")
            echo -e "${RED}🛡️ Risk Management Capabilities:${NC}"
            echo "   • Portfolio optimization"
            echo "   • Risk assessment"
            echo "   • Compliance monitoring"
            echo "   • VaR calculations"
            ;;
        "grpc-graphql-management-agent")
            echo -e "${GREEN}🔗 API Management Capabilities:${NC}"
            echo "   • gRPC service design"
            echo "   • GraphQL schema optimization"
            echo "   • Real-time communication"
            echo "   • Service mesh management"
            ;;
        "web-researcher-agent")
            echo -e "${BLUE}🔬 Web Researcher Capabilities:${NC}"
            echo "   • Market trend analysis"
            echo "   • Competitive intelligence"
            echo "   • Social sentiment monitoring"
            echo "   • Data gathering and validation"
            ;;
        "bloom-telegram-agent")
            echo -e "${PURPLE}📱 Bloom/Telegram Capabilities:${NC}"
            echo "   • Telegram bot development"
            echo "   • Social media integration"
            echo "   • Community management"
            echo "   • Real-time notifications"
            ;;
        *)
            echo -e "${CYAN}🎯 Main Orchestrator:${NC} Coordinating all agents"
            ;;
    esac
}

# Main execution
if [ $# -eq 0 ]; then
    echo -e "${RED}Usage: $0 <user_input>${NC}"
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p .claude/logs

# Analyze the input
user_input="$*"
suggested_agent=$(analyze_and_delegate "$user_input")

# Show agent capabilities
show_agent_capabilities "$suggested_agent"

# Output final recommendation
echo -e "\n${GREEN}💡 Recommendation:${NC} Use the ${YELLOW}$suggested_agent${NC} for this task"
