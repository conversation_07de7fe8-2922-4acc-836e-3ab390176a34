# Claude Hooks for Agent Delegation System

This directory contains the Claude hooks system that automates agent delegation and workflow coordination for the Solana memecoin trading agent project.

## 🎯 Overview

The hooks system provides intelligent automation for:
- **Agent Delegation**: Automatically suggests the most appropriate sub-agent based on task analysis
- **Workflow Coordination**: Orchestrates complex multi-agent workflows
- **Quality Gates**: Enforces quality checks and triggers appropriate reviews
- **Project Monitoring**: Tracks project progress and agent activities

## 📁 Files Structure

```
.claude/
├── settings.json           # Main hooks configuration
├── hooks/
│   ├── agent-delegation.sh    # Intelligent agent delegation
│   ├── workflow-coordinator.sh # Multi-agent workflow management
│   ├── quality-gate.sh        # Quality assurance automation
│   └── README.md              # This documentation
└── logs/
    ├── delegation.log         # Agent delegation history
    ├── workflow.log          # Workflow execution logs
    └── quality-gates.log     # Quality gate events
```

## 🔧 Hook Scripts

### 1. Agent Delegation (`agent-delegation.sh`)

Analyzes user input and suggests the most appropriate agent for the task.

**Usage:**
```bash
.claude/hooks/agent-delegation.sh "implement machine learning model for price prediction"
# Output: 🤖 [DELEGATION] AI/ML task detected → Delegating to Senior AI Engineer agent
```

**Supported Keywords:**
- **AI/ML**: `ai`, `ml`, `algorithm`, `model`, `neural`, `prediction`, `tensorflow`, `pytorch`
- **Security**: `security`, `audit`, `review`, `vulnerability`, `quality`, `test`
- **Architecture**: `architecture`, `database`, `scaling`, `infrastructure`, `microservices`
- **Solana**: `solana`, `blockchain`, `wallet`, `memecoin`, `dex`, `token`, `web3`
- **Risk**: `risk`, `portfolio`, `compliance`, `var`, `position`, `drawdown`
- **API**: `api`, `grpc`, `graphql`, `service`, `endpoint`, `communication`
- **Research**: `research`, `market`, `trend`, `competitive`, `analysis`, `sentiment`
- **Social**: `telegram`, `social`, `community`, `notification`, `bot`, `discord`

### 2. Workflow Coordinator (`workflow-coordinator.sh`)

Manages complex multi-agent workflows with step-by-step coordination.

**Available Workflows:**

#### Feature Development
```bash
.claude/hooks/workflow-coordinator.sh feature.development
```
**Steps:**
1. Web Researcher → Market analysis and user needs
2. Senior AI Engineer → Algorithm development
3. Backend Architect → System design
4. Solana Agent → Blockchain integration
5. Risk Management → Safety protocols
6. gRPC/GraphQL → API integration
7. Code Reviewer → Security audit
8. Bloom/Telegram → User interface

#### Trading Strategy Implementation
```bash
.claude/hooks/workflow-coordinator.sh trading.strategy
```
**Steps:**
1. Web Researcher → Market trend analysis
2. Senior AI Engineer → Algorithm development
3. Risk Management → Risk parameters
4. Solana Agent → Blockchain execution
5. Backend Architect → Performance optimization
6. Code Reviewer → Security validation
7. gRPC/GraphQL → Real-time data feeds
8. Bloom/Telegram → User alerts

#### System Optimization
```bash
.claude/hooks/workflow-coordinator.sh system.optimization
```
**Steps:**
1. Backend Architect → Performance analysis
2. Senior AI Engineer → Algorithm optimization
3. gRPC/GraphQL → API optimization
4. Solana Agent → Blockchain efficiency
5. Risk Management → Risk-adjusted optimization
6. Code Reviewer → Code optimization
7. Web Researcher → Competitive benchmarking
8. Bloom/Telegram → User experience optimization

### 3. Quality Gate (`quality-gate.sh`)

Enforces quality gates and triggers appropriate agent reviews based on operations.

**Usage:**
```bash
# Check quality gates for a commit
.claude/hooks/quality-gate.sh commit "trading strategy update" "strategy.py risk.js"

# Check quality gates for production deployment
.claude/hooks/quality-gate.sh deploy "production release" "all-files"

# View quality gate status
.claude/hooks/quality-gate.sh status

# Simulate quality gate scenarios
.claude/hooks/quality-gate.sh simulate production_deploy
```

**Quality Gate Rules:**
- **🚨 REQUIRED**: Production deployments, security files, financial operations
- **📋 RECOMMENDED**: Source code changes, trading strategies, architecture changes
- **✅ OPTIONAL**: Documentation, configuration, non-critical changes

## ⚙️ Configuration

### Main Hooks Configuration (`.claude/settings.json`)

The main configuration file defines hook triggers and matchers:

```json
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "codebase-retrieval",
        "hooks": [
          {
            "type": "command",
            "command": "echo '🔍 [MAIN ORCHESTRATOR] Analyzing codebase for agent delegation opportunities...'"
          }
        ]
      }
    ],
    "PostToolUse": [
      {
        "matcher": "save-file",
        "hooks": [
          {
            "type": "command",
            "command": "echo '💾 [MAIN ORCHESTRATOR] New file created - evaluating delegation needs...'"
          }
        ]
      }
    ],
    "AgentDelegation": [
      {
        "matcher": "AI|ML|algorithm|model|neural",
        "hooks": [
          {
            "type": "command",
            "command": "echo '🤖 [DELEGATION] AI/ML task detected → Delegating to Senior AI Engineer agent'"
          }
        ]
      }
    ]
  }
}
```

## 🚀 Usage Examples

### Automatic Agent Delegation

When you perform actions that match delegation patterns, hooks automatically suggest appropriate agents:

```bash
# Triggers AI Engineer delegation
"Implement a neural network for memecoin price prediction"

# Triggers Code Reviewer delegation  
"Review this smart contract for security vulnerabilities"

# Triggers Solana Agent delegation
"Create a wallet integration for Jupiter DEX"

# Triggers Risk Management delegation
"Calculate portfolio VaR and position sizing"
```

### Multi-Agent Workflows

Coordinate complex projects involving multiple agents:

```bash
# Start a new feature development workflow
.claude/hooks/workflow-coordinator.sh feature.development

# Implement a new trading strategy
.claude/hooks/workflow-coordinator.sh trading.strategy

# Optimize system performance
.claude/hooks/workflow-coordinator.sh system.optimization
```

### Quality Assurance

Ensure quality gates are met before critical operations:

```bash
# Before committing code
.claude/hooks/quality-gate.sh commit "security update" "wallet-manager.js"

# Before production deployment
.claude/hooks/quality-gate.sh deploy "mainnet release" "trading-engine.py"

# Check current quality status
.claude/hooks/quality-gate.sh status
```

## 📊 Monitoring and Logs

All hook activities are logged for monitoring and analysis:

- **`delegation.log`**: Records all agent delegation decisions
- **`workflow.log`**: Tracks multi-agent workflow execution
- **`quality-gates.log`**: Logs quality gate events and decisions

**Example log entries:**
```
[2024-01-15 10:30:45] DELEGATION: senior-ai-engineer - AI/ML keywords detected
[2024-01-15 10:31:20] WORKFLOW: Feature Development | STEP: 1 | AGENT: Web Researcher
[2024-01-15 10:32:10] QUALITY_GATE: CODE_REVIEW | STATUS: REQUIRED | DETAILS: commit
```

## 🔄 Integration with Main Orchestrator

The hooks system integrates seamlessly with the Main Orchestrator Agent:

1. **Automatic Triggering**: Hooks fire based on tool usage and keyword detection
2. **Delegation Suggestions**: Provide intelligent agent recommendations
3. **Workflow Coordination**: Guide complex multi-agent collaborations
4. **Quality Enforcement**: Ensure proper reviews and validations

## 🛠️ Customization

You can customize the hooks system by:

1. **Adding New Matchers**: Extend keyword patterns in `settings.json`
2. **Creating Custom Workflows**: Add new workflow types to `workflow-coordinator.sh`
3. **Modifying Quality Gates**: Adjust quality rules in `quality-gate.sh`
4. **Adding New Hooks**: Create additional hook scripts for specific needs

## 🎯 Best Practices

1. **Use Descriptive Keywords**: Include relevant keywords in your requests to trigger appropriate delegations
2. **Follow Workflows**: Use established workflows for complex multi-agent tasks
3. **Respect Quality Gates**: Allow quality checks to complete before proceeding
4. **Monitor Logs**: Regularly check logs to understand delegation patterns and optimize workflows
5. **Test Scenarios**: Use simulation features to test quality gates and workflows

The hooks system ensures that the right agent is always working on the right task, with proper coordination and quality assurance throughout the development process.
