# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a documentation repository for a Solana memecoin trading agent project. The repository contains comprehensive documentation about Claude Code features, MCP (Model Context Protocol) servers, hooks, troubleshooting guides, and Python SDK information.

## Architecture

The repository is organized as a collection of standalone markdown documentation files:

- **ClaudeCode.md**: Complete guide to Claude Code subagents and configuration
- **ModelConextProtocol.md**: Comprehensive MCP server integration documentation
- **ClaudeHooks.md**: Event-driven automation with hooks
- **PythonSDK.md**: Python SDK documentation and examples
- **OutputStyles.md**: Custom output styling configuration
- **TroubleShooting.md**: Common issues and solutions

### Specialized Subagents (.claude/agents/)

The repository includes 8 specialized Claude Code subagents for the Solana memecoin trading project:

- **solana-memecoins-wallets-agent.md**: Solana blockchain integration, memecoin analysis, wallet management
- **risk-management-agent.md**: Trading risk assessment and portfolio management
- **senior-backend-architect.md**: System architecture and backend infrastructure
- **grpc-graphql-management-agent.md**: API management and service integration
- **bloom-telegram-agent.md**: Telegram bot integration and social trading features
- **web-researcher-agent.md**: Market research and data gathering
- **code-reviewer.md**: Code quality assurance and review processes
- **senior-ai-engineer.md**: AI/ML implementation and optimization

## Common Commands

Since this is a documentation repository with no code, typical development commands are not applicable. However, you can:

```bash
# View main documentation files
cat ClaudeCode.md
cat ModelConextProtocol.md

# View specialized subagent configurations
ls .claude/agents/
cat .claude/agents/solana-memecoins-wallets-agent.md

# Search for specific topics across all docs
grep -r "subagent" *.md
grep -r "MCP server" *.md
grep -r "Solana" .claude/agents/

# Find specific agent configurations
grep -l "memecoin" .claude/agents/*.md
grep -l "risk" .claude/agents/*.md
```

## Key Documentation Areas

### Subagents (ClaudeCode.md)
- Creating and managing specialized AI subagents
- Configuration with YAML frontmatter
- Tool permissions and model selection
- Project vs user scope management

### MCP Integration (ModelConextProtocol.md)
- Connecting to external tools and services
- Authentication with OAuth 2.0
- Local stdio, remote SSE, and HTTP server configurations
- Popular MCP servers for development workflows

### Hooks System (ClaudeHooks.md)
- Event-driven automation and workflow triggers
- Custom shell command execution on tool events
- Hook configuration and management

### Python SDK (PythonSDK.md)
- Python-specific tooling and integration patterns
- SDK usage examples and best practices

### Solana Trading Agent Specializations

The subagents are configured specifically for Solana memecoin trading:

- **Blockchain Integration**: Solana RPC APIs, Web3.js, Anchor framework
- **Wallet Management**: Multi-sig wallets, hardware wallet integration, key management
- **Trading Logic**: DEX integration (Jupiter, Raydium, Orca), slippage protection, MEV protection
- **Risk Management**: Portfolio analysis, position sizing, stop-loss mechanisms
- **Social Features**: Telegram bot integration, community engagement tools
- **System Architecture**: Microservices, event-driven design, scalable backend infrastructure

## Working with This Repository

When making changes to documentation:

1. **Consistency**: Follow the established markdown formatting and structure patterns
2. **Cross-references**: Update related sections when making changes that affect multiple documents
3. **Examples**: Maintain practical, working examples in code blocks
4. **Accuracy**: Ensure all command examples and configurations are current and tested

## Content Organization

The documentation follows a logical hierarchy:
- Each .md file is self-contained but cross-references related topics
- Code examples use consistent formatting and realistic scenarios
- Configuration examples include both basic and advanced use cases
- Troubleshooting sections provide actionable solutions