# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a documentation repository for a Solana memecoin trading agent project. The repository contains comprehensive documentation about Claude Code features, MCP (Model Context Protocol) servers, hooks, troubleshooting guides, and Python SDK information.

## Architecture

The repository is organized as a collection of standalone markdown documentation files:

- **ClaudeCode.md**: Complete guide to Claude Code subagents and configuration
- **ModelConextProtocol.md**: Comprehensive MCP server integration documentation
- **ClaudeHooks.md**: Event-driven automation with hooks
- **PythonSDK.md**: Python SDK documentation and examples
- **OutputStyles.md**: Custom output styling configuration
- **TroubleShooting.md**: Common issues and solutions
- **HeadlessMode.md**: Programmatic CLI usage without interactive UI

### Specialized Subagents (.claude/agents/)

The repository includes 9 specialized Claude Code subagents for the Solana memecoin trading project:

- **solana-memecoins-wallets-agent.md**: Solana blockchain integration, memecoin analysis, wallet management
- **risk-management-agent.md**: Trading risk assessment and portfolio management
- **senior-backend-architect.md**: System architecture and backend infrastructure
- **grpc-graphql-management-agent.md**: API management and service integration
- **bloom-telegram-agent.md**: Telegram bot integration and social trading features
- **web-researcher-agent.md**: Market research and data gathering
- **code-reviewer.md**: Code quality assurance and review processes
- **senior-ai-engineer.md**: AI/ML implementation and optimization
- **main-orchestrator.md**: PRIMARY orchestrator for multi-agent coordination and strategic planning

## Common Commands

Since this is a documentation repository with no code, typical development commands are not applicable. However, you can:

```bash
# View main documentation files
cat ClaudeCode.md
cat ModelConextProtocol.md

# View specialized subagent configurations
ls .claude/agents/
cat .claude/agents/solana-memecoins-wallets-agent.md

# Search for specific topics across all docs
grep -r "subagent" *.md
grep -r "MCP server" *.md
grep -r "Solana" .claude/agents/

# Find specific agent configurations
grep -l "memecoin" .claude/agents/*.md
grep -l "risk" .claude/agents/*.md

# Run Claude Code programmatically (headless mode)
claude -p "query" --output-format json
claude -p "Stage my changes and write commits" --allowedTools "Bash,Read"
claude --resume session-id  # Resume previous session
claude --continue           # Continue most recent conversation
```

## Key Documentation Areas

### Subagents (ClaudeCode.md)
- Creating and managing specialized AI subagents
- Configuration with YAML frontmatter
- Tool permissions and model selection
- Project vs user scope management

### MCP Integration (ModelConextProtocol.md)
- Connecting to external tools and services
- Authentication with OAuth 2.0
- Local stdio, remote SSE, and HTTP server configurations
- Popular MCP servers for development workflows

### Hooks System (ClaudeHooks.md)
- Event-driven automation and workflow triggers
- Custom shell command execution on tool events
- Hook configuration and management

### Python SDK (PythonSDK.md)
- Python-specific tooling and integration patterns
- SDK usage examples and best practices

### Headless Mode (HeadlessMode.md)
- Programmatic execution with `claude` command
- Non-interactive mode with `--print` flag
- Output format options (text, json, stream-json)
- Session management and continuation

### Solana Trading Agent Specializations

The subagents are configured specifically for Solana memecoin trading:

- **Blockchain Integration**: Solana RPC APIs, Web3.js, Anchor framework
- **Wallet Management**: Multi-sig wallets, hardware wallet integration, key management
- **Trading Logic**: DEX integration (Jupiter, Raydium, Orca), slippage protection, MEV protection
- **Risk Management**: Portfolio analysis, position sizing, stop-loss mechanisms
- **Social Features**: Telegram bot integration, community engagement tools
- **System Architecture**: Microservices, event-driven design, scalable backend infrastructure

## Working with This Repository

When making changes to documentation:

1. **Consistency**: Follow the established markdown formatting and structure patterns
2. **Cross-references**: Update related sections when making changes that affect multiple documents
3. **Examples**: Maintain practical, working examples in code blocks
4. **Accuracy**: Ensure all command examples and configurations are current and tested

## Multi-Agent Orchestration

The **main-orchestrator** agent serves as the PRIMARY coordinator for the entire project and should be used proactively for:

- Complex multi-agent workflows requiring coordination between multiple specialists
- Strategic planning and architecture decisions
- High-level project management and task delegation
- Cross-functional integration and conflict resolution
- System-wide coordination and quality assurance

### Agent Coordination Patterns

**When working with multiple agents simultaneously:**

1. **Use main-orchestrator first** for complex tasks requiring multiple specialists
2. **Sequential delegation**: main-orchestrator → specialized agents → integration
3. **Parallel coordination**: Multiple specialized agents working on related components
4. **Integration validation**: Ensure all components work together seamlessly

### Specialized Agent Expertise Areas

- **solana-memecoins-wallets-agent**: Solana RPC, DEX integration (Jupiter, Raydium), wallet security
- **risk-management-agent**: Position sizing, portfolio optimization, VaR calculations
- **senior-backend-architect**: Microservices, database design, system scalability
- **grpc-graphql-management-agent**: API design, real-time data streams, service mesh
- **bloom-telegram-agent**: Bot development, social monitoring, community engagement
- **web-researcher-agent**: Market intelligence, competitive analysis, trend identification
- **code-reviewer**: Security audits, vulnerability assessment, quality standards
- **senior-ai-engineer**: ML models, trading algorithms, performance optimization

## Content Organization

The documentation follows a logical hierarchy:
- Each .md file is self-contained but cross-references related topics
- Agent configurations include YAML frontmatter with specific tool permissions
- Code examples use consistent formatting and realistic scenarios
- Configuration examples include both basic and advanced use cases
- Troubleshooting sections provide actionable solutions

## Automated Workflow System (.claude/hooks/)

The repository includes an intelligent hooks system for automated agent delegation and workflow coordination:

- **agent-delegation.sh**: Automatically suggests appropriate agents based on keyword analysis
- **workflow-coordinator.sh**: Manages complex multi-agent workflows (feature development, trading strategy, system optimization)
- **quality-gate.sh**: Enforces quality gates and triggers reviews based on operations
- **settings.json**: Main hooks configuration with pre/post tool use triggers

## Development Workflow Patterns

### For Trading Strategy Development:
1. **web-researcher-agent**: Market analysis and opportunity identification
2. **senior-ai-engineer**: Algorithm development and backtesting
3. **risk-management-agent**: Risk parameters and safety protocols
4. **solana-memecoins-wallets-agent**: Blockchain implementation
5. **code-reviewer**: Security validation and quality assurance

### For System Architecture Changes:
1. **main-orchestrator**: Overall planning and coordination
2. **senior-backend-architect**: Architecture design and infrastructure
3. **grpc-graphql-management-agent**: API and service integration
4. **code-reviewer**: Implementation review and validation
5. **All relevant specialists**: Component-specific implementation